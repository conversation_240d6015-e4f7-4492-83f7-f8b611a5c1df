# 数据库服务使用指南

本项目使用 Sembast 数据库实现了三个主要的数据表和对应的服务类，用于管理发票自动化系统的数据。

## 数据模型

### 1. 交易方信息表 (Party)
存储供应商和客户信息：
- **id**: UUID主键（自动生成）
- **name**: 名称
- **abn**: Australian Business Number
- **tfn**: Tax File Number
- **phoneNumber**: 电话号码
- **address**: 地址
- **type**: 交易方类型（供应商/客户）
- **createdAt/updatedAt**: 创建/更新时间

### 2. 交易记录表 (Transaction)
存储交易信息：
- **id**: UUID主键（自动生成）
- **partyId**: 交易方ID（外键）
- **amount**: 交易金额
- **transactionTime**: 交易时间
- **description**: 交易描述
- **createdAt/updatedAt**: 创建/更新时间

### 3. 发票表 (Invoice)
存储发票信息：
- **id**: UUID主键（自动生成）
- **transactionId**: 交易记录ID（外键）
- **issuedAt**: 开具时间
- **status**: 发票状态（草稿/已发送/已支付/逾期/已取消）
- **invoiceNumber**: 发票号码
- **dueDate**: 到期日期
- **notes**: 备注
- **createdAt/updatedAt**: 创建/更新时间

## 服务类

### DatabaseService
数据库管理服务，负责数据库的初始化、连接和清理。

```dart
final databaseService = DatabaseService();
await databaseService.clearAllData(); // 清空所有数据
await databaseService.close(); // 关闭数据库
```

### PartyService
交易方信息管理服务：

```dart
final partyService = PartyService();

// 创建交易方
final party = Party(
  name: 'ABC公司',
  abn: '***********',
  type: PartyType.supplier,
);
final createdParty = await partyService.create(party);

// 查询操作
final party = await partyService.getById(id);
final allParties = await partyService.getAll();
final suppliers = await partyService.getByType(PartyType.supplier);
final searchResults = await partyService.searchByName('ABC');

// 更新和删除
await partyService.update(updatedParty);
await partyService.delete(id);

// 统计
final count = await partyService.count();
final supplierCount = await partyService.countByType(PartyType.supplier);
```

### TransactionService
交易记录管理服务：

```dart
final transactionService = TransactionService();

// 创建交易记录
final transaction = Transaction(
  partyId: partyId,
  amount: 1500.00,
  transactionTime: DateTime.now(),
  description: '采购办公用品',
);
final createdTransaction = await transactionService.create(transaction);

// 查询操作
final transaction = await transactionService.getById(id);
final allTransactions = await transactionService.getAll();
final partyTransactions = await transactionService.getByPartyId(partyId);
final dateRangeTransactions = await transactionService.getByDateRange(startDate, endDate);
final amountRangeTransactions = await transactionService.getByAmountRange(minAmount, maxAmount);

// 更新和删除
await transactionService.update(updatedTransaction);
await transactionService.delete(id);

// 统计
final count = await transactionService.count();
final totalAmount = await transactionService.getTotalAmount();
final partyTotalAmount = await transactionService.getTotalAmountByPartyId(partyId);
```

### InvoiceService
发票管理服务：

```dart
final invoiceService = InvoiceService();

// 创建发票
final invoice = Invoice(
  transactionId: transactionId,
  issuedAt: DateTime.now(),
  invoiceNumber: 'INV-001',
  status: InvoiceStatus.draft,
  dueDate: DateTime.now().add(Duration(days: 30)),
);
final createdInvoice = await invoiceService.create(invoice);

// 查询操作
final invoice = await invoiceService.getById(id);
final allInvoices = await invoiceService.getAll();
final transactionInvoice = await invoiceService.getByTransactionId(transactionId);
final statusInvoices = await invoiceService.getByStatus(InvoiceStatus.sent);
final dateRangeInvoices = await invoiceService.getByDateRange(startDate, endDate);
final overdueInvoices = await invoiceService.getOverdueInvoices();
final invoiceByNumber = await invoiceService.getByInvoiceNumber('INV-001');

// 更新和删除
await invoiceService.update(updatedInvoice);
await invoiceService.updateStatus(id, InvoiceStatus.paid);
await invoiceService.delete(id);

// 统计
final count = await invoiceService.count();
final statusCount = await invoiceService.countByStatus(InvoiceStatus.draft);
```

## 使用示例

查看 `lib/services/example_usage.dart` 文件获取完整的使用示例。

```dart
final example = DatabaseUsageExample();
await example.runExample(); // 运行完整示例
await example.cleanupExample(); // 清理示例数据
```

## 测试

运行数据库服务测试：

```bash
flutter test test/database_services_test.dart
```

测试覆盖了所有主要功能：
- 创建和检索数据
- 查询和过滤
- 更新和删除
- 统计功能
- 外键约束验证

## 特性

1. **自动UUID生成**: 每个表都使用UUID作为主键，插入时自动生成
2. **外键约束**: 交易记录引用交易方，发票引用交易记录，创建时会验证外键存在
3. **时间戳**: 所有记录都有创建时间和更新时间
4. **丰富的查询**: 支持按各种条件查询和过滤
5. **统计功能**: 提供计数、求和等统计功能
6. **测试支持**: 支持内存数据库用于单元测试
7. **错误处理**: 完善的异常处理和错误信息

## 注意事项

1. 在生产环境中，数据库文件存储在应用文档目录
2. 在测试环境中，使用内存数据库，测试结束后数据会自动清除
3. 所有服务类都是单例模式，确保数据库连接的一致性
4. 删除交易方或交易记录前，请确保没有相关的依赖数据
5. 日期时间使用ISO8601格式存储，确保跨平台兼容性
