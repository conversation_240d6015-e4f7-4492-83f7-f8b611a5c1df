import 'package:flutter/material.dart';
import '../../models/party.dart';
import '../../services/party_service.dart';
import '../details/party_detail_page.dart';

class PartyRecordsTab extends StatefulWidget {
  const PartyRecordsTab({super.key});

  @override
  State<PartyRecordsTab> createState() => _PartyRecordsTabState();
}

class _PartyRecordsTabState extends State<PartyRecordsTab> {
  final PartyService _partyService = PartyService();
  List<Party> _parties = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadParties();
  }

  Future<void> _loadParties() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final parties = await _partyService.getAll();
      setState(() {
        _parties = parties;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading parties: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_parties.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No parties found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Parties will be created automatically from transactions',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadParties,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _parties.length,
        itemBuilder: (context, index) {
          final party = _parties[index];
          return _PartyCard(
            party: party,
            onTap: () => _openPartyDetail(party),
          );
        },
      ),
    );
  }

  void _openPartyDetail(Party party) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PartyDetailPage(party: party),
      ),
    ).then((_) => _loadParties()); // Refresh list when returning
  }
}

class _PartyCard extends StatelessWidget {
  final Party party;
  final VoidCallback onTap;

  const _PartyCard({
    required this.party,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: _getTypeColor(party.type),
                    child: Icon(
                      _getTypeIcon(party.type),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          party.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          party.type.toString().split('.').last.toUpperCase(),
                          style: TextStyle(
                            fontSize: 12,
                            color: _getTypeColor(party.type),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (party.phoneNumber != null) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.phone,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      party.phoneNumber!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
              if (party.address != null && party.address!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        party.address!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getTypeColor(dynamic type) {
    String typeStr = type is PartyType ? type.toString().split('.').last : type?.toString() ?? '';
    switch (typeStr.toLowerCase()) {
      case 'customer':
        return Colors.blue;
      case 'supplier':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(dynamic type) {
    String typeStr = type is PartyType ? type.toString().split('.').last : type?.toString() ?? '';
    switch (typeStr.toLowerCase()) {
      case 'customer':
        return Icons.person;
      case 'supplier':
        return Icons.business;
      default:
        return Icons.account_circle;
    }
  }
}
