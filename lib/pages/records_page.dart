import 'package:flutter/material.dart';
import 'records/transaction_records_tab.dart';
import 'records/invoice_records_tab.dart';
import 'records/party_records_tab.dart';

class RecordsPage extends StatefulWidget {
  const RecordsPage({super.key});

  @override
  State<RecordsPage> createState() => _RecordsPageState();
}

class _RecordsPageState extends State<RecordsPage> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Records'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.receipt),
              text: 'Transactions',
            ),
            Tab(
              icon: Icon(Icons.description),
              text: 'Invoices',
            ),
            Tab(
              icon: Icon(Icons.people),
              text: 'Parties',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          TransactionRecordsTab(),
          InvoiceRecordsTab(),
          PartyRecordsTab(),
        ],
      ),
    );
  }
}
