import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/receipt_data.dart';
import '../models/receipt_item.dart';

class ReceiptResultPage extends StatefulWidget {
  final ReceiptData receiptData;

  const ReceiptResultPage({
    super.key,
    required this.receiptData,
  });

  @override
  State<ReceiptResultPage> createState() => _ReceiptResultPageState();
}

class _ReceiptResultPageState extends State<ReceiptResultPage> {
  late ReceiptData _receiptData;
  final List<TextEditingController> _itemNameControllers = [];
  final List<TextEditingController> _itemPriceControllers = [];
  final List<TextEditingController> _itemQuantityControllers = [];
  final TextEditingController _merchantController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _receiptData = widget.receiptData;
    _initializeControllers();
  }

  void _initializeControllers() {
    _merchantController.text = _receiptData.merchantName;
    _dateController.text = _receiptData.date;
    _addressController.text = _receiptData.address;

    _itemNameControllers.clear();
    _itemPriceControllers.clear();
    _itemQuantityControllers.clear();

    for (final item in _receiptData.items) {
      _itemNameControllers.add(TextEditingController(text: item.name));
      _itemPriceControllers.add(TextEditingController(text: item.price.toStringAsFixed(2)));
      _itemQuantityControllers.add(TextEditingController(text: item.quantity.toString()));
    }
  }

  @override
  void dispose() {
    _merchantController.dispose();
    _dateController.dispose();
    _addressController.dispose();
    for (final controller in _itemNameControllers) {
      controller.dispose();
    }
    for (final controller in _itemPriceControllers) {
      controller.dispose();
    }
    for (final controller in _itemQuantityControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Receipt Details'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            onPressed: _saveReceipt,
            icon: const Icon(Icons.save),
            tooltip: 'Save Receipt',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildReceiptHeader(),
            const SizedBox(height: 24),
            _buildItemsSection(),
            const SizedBox(height: 24),
            _buildTotalsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildReceiptHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Receipt Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _merchantController,
              decoration: const InputDecoration(
                labelText: 'Merchant Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _dateController,
              decoration: const InputDecoration(
                labelText: 'Date',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'Address',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Items',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: _addNewItem,
                  icon: const Icon(Icons.add),
                  tooltip: 'Add Item',
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _receiptData.items.length,
              itemBuilder: (context, index) => _buildItemRow(index),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemRow(int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  flex: 3,
                  child: TextFormField(
                    controller: _itemNameControllers[index],
                    decoration: const InputDecoration(
                      labelText: 'Item Name',
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    controller: _itemQuantityControllers[index],
                    decoration: const InputDecoration(
                      labelText: 'Qty',
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    controller: _itemPriceControllers[index],
                    decoration: const InputDecoration(
                      labelText: 'Price',
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  ),
                ),
                IconButton(
                  onPressed: () => _removeItem(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  tooltip: 'Remove Item',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Totals',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildTotalRow('Subtotal', _receiptData.subtotal),
            _buildTotalRow('Tax', _receiptData.tax),
            const Divider(),
            _buildTotalRow('Total', _receiptData.total, isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${_receiptData.currency} ${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 18 : 16,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  void _addNewItem() {
    setState(() {
      _receiptData = _receiptData.copyWith(
        items: [
          ..._receiptData.items,
          ReceiptItem(name: '', price: 0.0, quantity: 1, total: 0.0),
        ],
      );
      _itemNameControllers.add(TextEditingController());
      _itemPriceControllers.add(TextEditingController(text: '0.00'));
      _itemQuantityControllers.add(TextEditingController(text: '1'));
    });
  }

  void _removeItem(int index) {
    if (_receiptData.items.length > 1) {
      setState(() {
        final newItems = List<ReceiptItem>.from(_receiptData.items);
        newItems.removeAt(index);
        _receiptData = _receiptData.copyWith(items: newItems);
        
        _itemNameControllers[index].dispose();
        _itemPriceControllers[index].dispose();
        _itemQuantityControllers[index].dispose();
        
        _itemNameControllers.removeAt(index);
        _itemPriceControllers.removeAt(index);
        _itemQuantityControllers.removeAt(index);
      });
    }
  }

  void _saveReceipt() {
    // TODO: Implement save functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Receipt saved successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
