import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnvChecker {
  static bool isConfigured() {
    try {
      final apiKey = dotenv.env['GEMINI_API_KEY'];
      return apiKey != null && apiKey.isNotEmpty;
    } catch (e) {
      print('Error checking environment configuration: $e');
      return false;
    }
  }
  
  static String getApiKey() {
    try {
      return dotenv.env['GEMINI_API_KEY'] ?? '';
    } catch (e) {
      print('Error getting API key: $e');
      return '';
    }
  }
  
  static String getConfigurationStatus() {
    if (!isConfigured()) {
      return 'Environment variables not properly configured. Please check your .env file.';
    }
    
    final apiKey = getApiKey();
    if (apiKey.isEmpty) {
      return 'GEMINI_API_KEY is empty. Please add your API key to the .env file.';
    }
    
    if (apiKey.length < 20) {
      return 'GEMINI_API_KEY appears to be invalid. Please check your API key.';
    }
    
    return 'Configuration looks good!';
  }
}
