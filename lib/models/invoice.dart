import 'package:uuid/uuid.dart';

/// 发票状态枚举
enum InvoiceStatus {
  draft, // 草稿
  sent, // 已发送
  paid, // 已支付
  overdue, // 逾期
  cancelled, // 已取消
}

/// 发票模型
class Invoice {
  final String id;
  final String transactionId; // 交易记录ID（外键）
  final DateTime issuedAt; // 开具时间
  final InvoiceStatus status; // 发票状态
  final String? invoiceNumber; // 发票号码
  final DateTime? dueDate; // 到期日期
  final String? notes; // 备注
  final DateTime createdAt;
  final DateTime updatedAt;

  Invoice({
    String? id,
    required this.transactionId,
    required this.issuedAt,
    this.status = InvoiceStatus.draft,
    this.invoiceNumber,
    this.dueDate,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// 从Map创建Invoice对象
  factory Invoice.fromMap(Map<String, dynamic> map) {
    return Invoice(
      id: map['id'] as String,
      transactionId: map['transactionId'] as String,
      issuedAt: DateTime.parse(map['issuedAt'] as String),
      status: InvoiceStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => InvoiceStatus.draft,
      ),
      invoiceNumber: map['invoiceNumber'] as String?,
      dueDate: map['dueDate'] != null 
          ? DateTime.parse(map['dueDate'] as String) 
          : null,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'transactionId': transactionId,
      'issuedAt': issuedAt.toIso8601String(),
      'status': status.toString(),
      'invoiceNumber': invoiceNumber,
      'dueDate': dueDate?.toIso8601String(),
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 创建副本并更新指定字段
  Invoice copyWith({
    String? transactionId,
    DateTime? issuedAt,
    InvoiceStatus? status,
    String? invoiceNumber,
    DateTime? dueDate,
    String? notes,
  }) {
    return Invoice(
      id: id,
      transactionId: transactionId ?? this.transactionId,
      issuedAt: issuedAt ?? this.issuedAt,
      status: status ?? this.status,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      dueDate: dueDate ?? this.dueDate,
      notes: notes ?? this.notes,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'Invoice{id: $id, transactionId: $transactionId, issuedAt: $issuedAt, status: $status}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Invoice && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
