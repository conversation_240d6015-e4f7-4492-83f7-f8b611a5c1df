import 'package:uuid/uuid.dart';

/// 交易记录模型
class Transaction {
  final String id;
  final String partyId; // 交易方ID（外键）
  final double amount; // 交易金额
  final DateTime transactionTime; // 交易时间
  final String? description; // 交易描述（可选）
  final DateTime createdAt;
  final DateTime updatedAt;

  Transaction({
    String? id,
    required this.partyId,
    required this.amount,
    required this.transactionTime,
    this.description,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// 从Map创建Transaction对象
  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'] as String,
      partyId: map['partyId'] as String,
      amount: (map['amount'] as num).toDouble(),
      transactionTime: DateTime.parse(map['transactionTime'] as String),
      description: map['description'] as String?,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'partyId': partyId,
      'amount': amount,
      'transactionTime': transactionTime.toIso8601String(),
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 创建副本并更新指定字段
  Transaction copyWith({
    String? partyId,
    double? amount,
    DateTime? transactionTime,
    String? description,
  }) {
    return Transaction(
      id: id,
      partyId: partyId ?? this.partyId,
      amount: amount ?? this.amount,
      transactionTime: transactionTime ?? this.transactionTime,
      description: description ?? this.description,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'Transaction{id: $id, partyId: $partyId, amount: $amount, transactionTime: $transactionTime}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Transaction &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
