import 'receipt_item.dart';

class ReceiptData {
  final String merchantName;
  final String date;
  final String address;
  final List<ReceiptItem> items;
  final double subtotal;
  final double tax;
  final double total;
  final String currency;

  ReceiptData({
    required this.merchantName,
    required this.date,
    required this.address,
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.total,
    this.currency = 'USD',
  });

  factory ReceiptData.fromJson(Map<String, dynamic> json) {
    var itemsList = json['items'] as List? ?? [];
    List<ReceiptItem> items = itemsList
        .map((item) => ReceiptItem.fromJson(item as Map<String, dynamic>))
        .toList();

    return ReceiptData(
      merchantName: json['merchant_name'] ?? '',
      date: json['date'] ?? '',
      address: json['address'] ?? '',
      items: items,
      subtotal: (json['subtotal'] ?? 0).toDouble(),
      tax: (json['tax'] ?? 0).toDouble(),
      total: (json['total'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'USD',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'merchant_name': merchantName,
      'date': date,
      'address': address,
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'total': total,
      'currency': currency,
    };
  }

  ReceiptData copyWith({
    String? merchantName,
    String? date,
    String? address,
    List<ReceiptItem>? items,
    double? subtotal,
    double? tax,
    double? total,
    String? currency,
  }) {
    return ReceiptData(
      merchantName: merchantName ?? this.merchantName,
      date: date ?? this.date,
      address: address ?? this.address,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      total: total ?? this.total,
      currency: currency ?? this.currency,
    );
  }
}
