class ReceiptItem {
  final String name;
  final double price;
  final int quantity;
  final double total;

  ReceiptItem({
    required this.name,
    required this.price,
    required this.quantity,
    required this.total,
  });

  factory ReceiptItem.fromJson(Map<String, dynamic> json) {
    return ReceiptItem(
      name: json['name'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      quantity: (json['quantity'] ?? 1).toInt(),
      total: (json['total'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'price': price,
      'quantity': quantity,
      'total': total,
    };
  }

  ReceiptItem copyWith({
    String? name,
    double? price,
    int? quantity,
    double? total,
  }) {
    return ReceiptItem(
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      total: total ?? this.total,
    );
  }
}
