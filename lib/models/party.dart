import 'package:uuid/uuid.dart';

/// 交易方类型枚举
enum PartyType {
  supplier, // 供应商
  customer, // 客户
}

/// 交易方信息模型
class Party {
  final String id;
  final String name;
  final String? abn; // Australian Business Number
  final String? tfn; // Tax File Number
  final String? phoneNumber;
  final String? address;
  final PartyType type;
  final DateTime createdAt;
  final DateTime updatedAt;

  Party({
    String? id,
    required this.name,
    this.abn,
    this.tfn,
    this.phoneNumber,
    this.address,
    required this.type,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// 从Map创建Party对象
  factory Party.fromMap(Map<String, dynamic> map) {
    return Party(
      id: map['id'] as String,
      name: map['name'] as String,
      abn: map['abn'] as String?,
      tfn: map['tfn'] as String?,
      phoneNumber: map['phoneNumber'] as String?,
      address: map['address'] as String?,
      type: PartyType.values.firstWhere(
        (e) => e.toString() == map['type'],
      ),
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'abn': abn,
      'tfn': tfn,
      'phoneNumber': phoneNumber,
      'address': address,
      'type': type.toString(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 创建副本并更新指定字段
  Party copyWith({
    String? name,
    String? abn,
    String? tfn,
    String? phoneNumber,
    String? address,
    PartyType? type,
  }) {
    return Party(
      id: id,
      name: name ?? this.name,
      abn: abn ?? this.abn,
      tfn: tfn ?? this.tfn,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,
      type: type ?? this.type,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'Party{id: $id, name: $name, type: $type}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Party && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
