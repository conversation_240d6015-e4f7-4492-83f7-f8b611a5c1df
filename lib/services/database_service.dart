import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast_io.dart';
import 'package:sembast/sembast_memory.dart';

/// 数据库管理服务
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static const String _dbName = 'invoice_automate.db';
  Database? _database;
  bool _isTestMode = false;

  /// 获取数据库实例
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// 设置测试模式（使用内存数据库）
  void setTestMode(bool testMode) {
    _isTestMode = testMode;
  }

  /// 初始化数据库
  Future<Database> _initDatabase() async {
    try {
      if (_isTestMode) {
        // 测试模式使用内存数据库
        return await databaseFactoryMemory.openDatabase('test_db');
      } else {
        // 生产模式使用文件数据库
        final Directory appDocDir = await getApplicationDocumentsDirectory();
        final String dbPath = join(appDocDir.path, _dbName);
        return await databaseFactoryIo.openDatabase(dbPath);
      }
    } catch (e) {
      throw Exception('Failed to initialize database: $e');
    }
  }

  /// 关闭数据库
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// 清空所有数据（用于测试或重置）
  Future<void> clearAllData() async {
    final db = await database;
    // 清空所有store
    final storeNames = ['parties', 'transactions', 'invoices'];
    for (final storeName in storeNames) {
      final store = stringMapStoreFactory.store(storeName);
      await store.delete(db);
    }
  }

  /// 删除数据库文件
  Future<void> deleteDatabase() async {
    await close();
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String dbPath = join(appDocDir.path, _dbName);
      final File dbFile = File(dbPath);
      if (await dbFile.exists()) {
        await dbFile.delete();
      }
    } catch (e) {
      throw Exception('Failed to delete database: $e');
    }
  }
}
