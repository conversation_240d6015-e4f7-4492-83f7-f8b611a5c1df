import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/receipt_data.dart';
import '../utils/env_checker.dart';

class GeminiService {
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent';
  
  Future<ReceiptData?> analyzeReceipt(File imageFile) async {
    try {
      // Check environment configuration
      if (!EnvChecker.isConfigured()) {
        throw Exception(EnvChecker.getConfigurationStatus());
      }

      final apiKey = EnvChecker.getApiKey();
      if (apiKey.isEmpty) {
        throw Exception('Gemini API key not found. Please check your .env file and ensure it is properly loaded.');
      }

      // Read image file as bytes
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      // Determine MIME type based on file extension
      String mimeType = 'image/jpeg';
      final fileName = imageFile.path.toLowerCase();
      if (fileName.endsWith('.png')) {
        mimeType = 'image/png';
      } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
        mimeType = 'image/jpeg';
      } else if (fileName.endsWith('.webp')) {
        mimeType = 'image/webp';
      }

      // Prepare the request body
      final requestBody = {
        "contents": [
          {
            "parts": [
              {
                "text": """
Analyze this receipt/invoice image and extract the following information in JSON format:
{
  "merchant_name": "Name of the store/merchant",
  "date": "Date of purchase (YYYY-MM-DD format)",
  "address": "Store address",
  "items": [
    {
      "name": "Item name",
      "price": 0.00,
      "quantity": 1,
      "total": 0.00
    }
  ],
  "subtotal": 0.00,
  "tax": 0.00,
  "total": 0.00,
  "currency": "USD"
}

Please extract all visible items with their prices and quantities. If quantity is not specified, assume 1. Calculate totals accurately. Return only the JSON object, no additional text.
"""
              },
              {
                "inline_data": {
                  "mime_type": mimeType,
                  "data": base64Image
                }
              }
            ]
          }
        ],
        "generationConfig": {
            "thinkingConfig": {
              "thinkingBudget": 0
            },
          "temperature": 0.1,
          "topK": 32,
          "topP": 1,
          "maxOutputTokens": 4096,
        },
        "systemInstruction": {
          "parts": [
            {
              "text": "You are a receipt/invoice data extraction assistant. Extract information accurately and return only valid JSON."
            }
          ]
        }
      };

      // Make the API request
      final response = await http.post(
        Uri.parse('$_baseUrl?key=$apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final content = responseData['candidates']?[0]?['content']?['parts']?[0]?['text'];
        
        if (content != null) {
          // Clean the response to extract JSON
          String cleanedContent = content.toString().trim();
          
          // Remove markdown code blocks if present
          if (cleanedContent.startsWith('```json')) {
            cleanedContent = cleanedContent.substring(7);
          }
          if (cleanedContent.startsWith('```')) {
            cleanedContent = cleanedContent.substring(3);
          }
          if (cleanedContent.endsWith('```')) {
            cleanedContent = cleanedContent.substring(0, cleanedContent.length - 3);
          }
          
          cleanedContent = cleanedContent.trim();

          // Parse JSON with error handling
          try {
            final jsonData = jsonDecode(cleanedContent);
            return ReceiptData.fromJson(jsonData);
          } catch (jsonError) {
            print('JSON parsing error: $jsonError');
            print('Raw content: $cleanedContent');
            throw Exception('Failed to parse AI response as JSON: $jsonError');
          }
        }
      } else {
        throw Exception('Failed to analyze receipt: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Error analyzing receipt: $e');
      rethrow;
    }
    
    return null;
  }
}
