import '../models/party.dart';
import '../models/transaction.dart' as models;
import '../models/invoice.dart';
import 'party_service.dart';
import 'transaction_service.dart';
import 'invoice_service.dart';

/// 数据库服务使用示例
class DatabaseUsageExample {
  final PartyService _partyService = PartyService();
  final TransactionService _transactionService = TransactionService();
  final InvoiceService _invoiceService = InvoiceService();

  /// 完整的使用示例
  Future<void> runExample() async {
    try {
      print('=== 数据库服务使用示例 ===\n');

      // 1. 创建交易方
      print('1. 创建交易方...');
      final supplier = Party(
        name: 'ABC供应商有限公司',
        abn: '*********01',
        tfn: '*********',
        phoneNumber: '+61 2 1234 5678',
        address: '123 Business St, Sydney NSW 2000',
        type: PartyType.supplier,
      );

      final customer = Party(
        name: '客户公司',
        abn: '***********',
        phoneNumber: '+61 3 9876 5432',
        address: '456 Customer Ave, Melbourne VIC 3000',
        type: PartyType.customer,
      );

      final createdSupplier = await _partyService.create(supplier);
      final createdCustomer = await _partyService.create(customer);
      
      print('创建供应商: ${createdSupplier.name} (ID: ${createdSupplier.id})');
      print('创建客户: ${createdCustomer.name} (ID: ${createdCustomer.id})\n');

      // 2. 创建交易记录
      print('2. 创建交易记录...');
      final transaction1 = models.Transaction(
        partyId: createdSupplier.id,
        amount: 1500.00,
        transactionTime: DateTime.now().subtract(const Duration(days: 7)),
        description: '采购办公用品',
      );

      final transaction2 = models.Transaction(
        partyId: createdCustomer.id,
        amount: 2500.00,
        transactionTime: DateTime.now().subtract(const Duration(days: 3)),
        description: '销售产品',
      );

      final createdTransaction1 = await _transactionService.create(transaction1);
      final createdTransaction2 = await _transactionService.create(transaction2);
      
      print('创建交易1: ${createdTransaction1.description} - \$${createdTransaction1.amount}');
      print('创建交易2: ${createdTransaction2.description} - \$${createdTransaction2.amount}\n');

      // 3. 创建发票
      print('3. 创建发票...');
      final invoice1 = Invoice(
        transactionId: createdTransaction1.id,
        issuedAt: DateTime.now().subtract(const Duration(days: 6)),
        invoiceNumber: 'INV-001',
        dueDate: DateTime.now().add(const Duration(days: 30)),
        status: InvoiceStatus.sent,
        notes: '30天付款期限',
      );

      final invoice2 = Invoice(
        transactionId: createdTransaction2.id,
        issuedAt: DateTime.now().subtract(const Duration(days: 2)),
        invoiceNumber: 'INV-002',
        dueDate: DateTime.now().add(const Duration(days: 14)),
        status: InvoiceStatus.draft,
        notes: '14天付款期限',
      );

      final createdInvoice1 = await _invoiceService.create(invoice1);
      final createdInvoice2 = await _invoiceService.create(invoice2);
      
      print('创建发票1: ${createdInvoice1.invoiceNumber} - 状态: ${createdInvoice1.status}');
      print('创建发票2: ${createdInvoice2.invoiceNumber} - 状态: ${createdInvoice2.status}\n');

      // 4. 查询示例
      print('4. 查询示例...');
      
      // 获取所有供应商
      final suppliers = await _partyService.getByType(PartyType.supplier);
      print('供应商数量: ${suppliers.length}');
      
      // 获取所有客户
      final customers = await _partyService.getByType(PartyType.customer);
      print('客户数量: ${customers.length}');
      
      // 获取特定交易方的所有交易
      final supplierTransactions = await _transactionService.getByPartyId(createdSupplier.id);
      print('供应商交易数量: ${supplierTransactions.length}');
      
      // 计算总交易金额
      final totalAmount = await _transactionService.getTotalAmount();
      print('总交易金额: \$${totalAmount.toStringAsFixed(2)}');
      
      // 获取草稿状态的发票
      final draftInvoices = await _invoiceService.getByStatus(InvoiceStatus.draft);
      print('草稿发票数量: ${draftInvoices.length}');
      
      // 获取已发送状态的发票
      final sentInvoices = await _invoiceService.getByStatus(InvoiceStatus.sent);
      print('已发送发票数量: ${sentInvoices.length}\n');

      // 5. 更新示例
      print('5. 更新示例...');
      
      // 更新交易方信息
      final updatedSupplier = createdSupplier.copyWith(
        phoneNumber: '+61 2 1234 9999',
        address: '789 New Address St, Sydney NSW 2000',
      );
      await _partyService.update(updatedSupplier);
      print('更新供应商信息完成');
      
      // 更新发票状态
      await _invoiceService.updateStatus(createdInvoice2.id, InvoiceStatus.sent);
      print('更新发票状态完成');
      
      // 6. 统计信息
      print('\n6. 统计信息...');
      final partyCount = await _partyService.count();
      final transactionCount = await _transactionService.count();
      final invoiceCount = await _invoiceService.count();
      
      print('交易方总数: $partyCount');
      print('交易记录总数: $transactionCount');
      print('发票总数: $invoiceCount');
      
      print('\n=== 示例完成 ===');
      
    } catch (e) {
      print('示例执行出错: $e');
    }
  }

  /// 清理示例数据
  Future<void> cleanupExample() async {
    try {
      print('清理示例数据...');
      
      // 获取所有数据并删除
      final allInvoices = await _invoiceService.getAll();
      for (final invoice in allInvoices) {
        await _invoiceService.delete(invoice.id);
      }
      
      final allTransactions = await _transactionService.getAll();
      for (final transaction in allTransactions) {
        await _transactionService.delete(transaction.id);
      }
      
      final allParties = await _partyService.getAll();
      for (final party in allParties) {
        await _partyService.delete(party.id);
      }
      
      print('清理完成');
    } catch (e) {
      print('清理数据出错: $e');
    }
  }
}
