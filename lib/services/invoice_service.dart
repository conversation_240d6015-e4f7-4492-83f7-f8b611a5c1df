import 'package:sembast/sembast.dart';
import '../models/invoice.dart';
import 'database_service.dart';
import 'transaction_service.dart';

/// 发票数据库服务
class InvoiceService {
  static final InvoiceService _instance = InvoiceService._internal();
  factory InvoiceService() => _instance;
  InvoiceService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final TransactionService _transactionService = TransactionService();
  final StoreRef<String, Map<String, Object?>> _store = 
      stringMapStoreFactory.store('invoices');

  /// 创建新的发票
  Future<Invoice> create(Invoice invoice) async {
    try {
      // 验证交易记录是否存在
      final transactionExists = await _transactionService.exists(invoice.transactionId);
      if (!transactionExists) {
        throw Exception('Transaction with id ${invoice.transactionId} does not exist');
      }

      final db = await _databaseService.database;
      await _store.record(invoice.id).put(db, invoice.toMap());
      return invoice;
    } catch (e) {
      throw Exception('Failed to create invoice: $e');
    }
  }

  /// 根据ID获取发票
  Future<Invoice?> getById(String id) async {
    try {
      final db = await _databaseService.database;
      final map = await _store.record(id).get(db);
      return map != null ? Invoice.fromMap(map) : null;
    } catch (e) {
      throw Exception('Failed to get invoice by id: $e');
    }
  }

  /// 获取所有发票
  Future<List<Invoice>> getAll() async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        sortOrders: [SortOrder('issuedAt', false)], // 按开具时间倒序
      );
      final records = await _store.find(db, finder: finder);
      return records.map((record) => Invoice.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to get all invoices: $e');
    }
  }

  /// 根据交易记录ID获取发票
  Future<Invoice?> getByTransactionId(String transactionId) async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.equals('transactionId', transactionId),
      );
      final records = await _store.find(db, finder: finder);
      return records.isNotEmpty ? Invoice.fromMap(records.first.value) : null;
    } catch (e) {
      throw Exception('Failed to get invoice by transaction id: $e');
    }
  }

  /// 根据状态获取发票
  Future<List<Invoice>> getByStatus(InvoiceStatus status) async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.equals('status', status.toString()),
        sortOrders: [SortOrder('issuedAt', false)],
      );
      final records = await _store.find(db, finder: finder);
      return records.map((record) => Invoice.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to get invoices by status: $e');
    }
  }

  /// 根据日期范围获取发票
  Future<List<Invoice>> getByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.and([
          Filter.greaterThanOrEquals('issuedAt', startDate.toIso8601String()),
          Filter.lessThanOrEquals('issuedAt', endDate.toIso8601String()),
        ]),
        sortOrders: [SortOrder('issuedAt', false)],
      );
      final records = await _store.find(db, finder: finder);
      return records.map((record) => Invoice.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to get invoices by date range: $e');
    }
  }

  /// 获取逾期发票
  Future<List<Invoice>> getOverdueInvoices() async {
    try {
      final now = DateTime.now();
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.and([
          Filter.lessThan('dueDate', now.toIso8601String()),
          Filter.notEquals('status', InvoiceStatus.paid.toString()),
          Filter.notEquals('status', InvoiceStatus.cancelled.toString()),
        ]),
        sortOrders: [SortOrder('dueDate', true)], // 按到期日期升序
      );
      final records = await _store.find(db, finder: finder);
      return records.map((record) => Invoice.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to get overdue invoices: $e');
    }
  }

  /// 根据发票号码获取发票
  Future<Invoice?> getByInvoiceNumber(String invoiceNumber) async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.equals('invoiceNumber', invoiceNumber),
      );
      final records = await _store.find(db, finder: finder);
      return records.isNotEmpty ? Invoice.fromMap(records.first.value) : null;
    } catch (e) {
      throw Exception('Failed to get invoice by invoice number: $e');
    }
  }

  /// 更新发票
  Future<Invoice> update(Invoice invoice) async {
    try {
      // 验证交易记录是否存在
      final transactionExists = await _transactionService.exists(invoice.transactionId);
      if (!transactionExists) {
        throw Exception('Transaction with id ${invoice.transactionId} does not exist');
      }

      final db = await _databaseService.database;
      final updatedInvoice = invoice.copyWith();
      await _store.record(invoice.id).put(db, updatedInvoice.toMap());
      return updatedInvoice;
    } catch (e) {
      throw Exception('Failed to update invoice: $e');
    }
  }

  /// 更新发票状态
  Future<Invoice> updateStatus(String id, InvoiceStatus status) async {
    try {
      final invoice = await getById(id);
      if (invoice == null) {
        throw Exception('Invoice with id $id not found');
      }
      
      final updatedInvoice = invoice.copyWith(status: status);
      return await update(updatedInvoice);
    } catch (e) {
      throw Exception('Failed to update invoice status: $e');
    }
  }

  /// 删除发票
  Future<bool> delete(String id) async {
    try {
      final db = await _databaseService.database;
      final key = await _store.record(id).delete(db);
      return key != null;
    } catch (e) {
      throw Exception('Failed to delete invoice: $e');
    }
  }

  /// 检查发票是否存在
  Future<bool> exists(String id) async {
    try {
      final invoice = await getById(id);
      return invoice != null;
    } catch (e) {
      throw Exception('Failed to check if invoice exists: $e');
    }
  }

  /// 获取发票总数
  Future<int> count() async {
    try {
      final db = await _databaseService.database;
      return await _store.count(db);
    } catch (e) {
      throw Exception('Failed to count invoices: $e');
    }
  }

  /// 根据状态获取发票总数
  Future<int> countByStatus(InvoiceStatus status) async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.equals('status', status.toString()),
      );
      final records = await _store.find(db, finder: finder);
      return records.length;
    } catch (e) {
      throw Exception('Failed to count invoices by status: $e');
    }
  }
}
