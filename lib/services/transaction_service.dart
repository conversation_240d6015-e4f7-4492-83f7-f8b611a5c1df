import 'package:sembast/sembast.dart';
import '../models/transaction.dart' as models;
import 'database_service.dart';
import 'party_service.dart';

/// 交易记录数据库服务
class TransactionService {
  static final TransactionService _instance = TransactionService._internal();
  factory TransactionService() => _instance;
  TransactionService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final PartyService _partyService = PartyService();
  final StoreRef<String, Map<String, Object?>> _store = 
      stringMapStoreFactory.store('transactions');

  /// 创建新的交易记录
  Future<models.Transaction> create(models.Transaction transaction) async {
    try {
      // 验证交易方是否存在
      final partyExists = await _partyService.exists(transaction.partyId);
      if (!partyExists) {
        throw Exception('Party with id ${transaction.partyId} does not exist');
      }

      final db = await _databaseService.database;
      await _store.record(transaction.id).put(db, transaction.toMap());
      return transaction;
    } catch (e) {
      throw Exception('Failed to create transaction: $e');
    }
  }

  /// 根据ID获取交易记录
  Future<models.Transaction?> getById(String id) async {
    try {
      final db = await _databaseService.database;
      final map = await _store.record(id).get(db);
      return map != null ? models.Transaction.fromMap(map) : null;
    } catch (e) {
      throw Exception('Failed to get transaction by id: $e');
    }
  }

  /// 获取所有交易记录
  Future<List<models.Transaction>> getAll() async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        sortOrders: [SortOrder('transactionTime', false)], // 按交易时间倒序
      );
      final records = await _store.find(db, finder: finder);
      return records.map((record) => models.Transaction.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to get all transactions: $e');
    }
  }

  /// 根据交易方ID获取交易记录
  Future<List<models.Transaction>> getByPartyId(String partyId) async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.equals('partyId', partyId),
        sortOrders: [SortOrder('transactionTime', false)],
      );
      final records = await _store.find(db, finder: finder);
      return records.map((record) => models.Transaction.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to get transactions by party id: $e');
    }
  }

  /// 根据日期范围获取交易记录
  Future<List<models.Transaction>> getByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.and([
          Filter.greaterThanOrEquals('transactionTime', startDate.toIso8601String()),
          Filter.lessThanOrEquals('transactionTime', endDate.toIso8601String()),
        ]),
        sortOrders: [SortOrder('transactionTime', false)],
      );
      final records = await _store.find(db, finder: finder);
      return records.map((record) => models.Transaction.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to get transactions by date range: $e');
    }
  }

  /// 根据金额范围获取交易记录
  Future<List<models.Transaction>> getByAmountRange(double minAmount, double maxAmount) async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.and([
          Filter.greaterThanOrEquals('amount', minAmount),
          Filter.lessThanOrEquals('amount', maxAmount),
        ]),
        sortOrders: [SortOrder('amount', false)],
      );
      final records = await _store.find(db, finder: finder);
      return records.map((record) => models.Transaction.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to get transactions by amount range: $e');
    }
  }

  /// 更新交易记录
  Future<models.Transaction> update(models.Transaction transaction) async {
    try {
      // 验证交易方是否存在
      final partyExists = await _partyService.exists(transaction.partyId);
      if (!partyExists) {
        throw Exception('Party with id ${transaction.partyId} does not exist');
      }

      final db = await _databaseService.database;
      final updatedTransaction = transaction.copyWith();
      await _store.record(transaction.id).put(db, updatedTransaction.toMap());
      return updatedTransaction;
    } catch (e) {
      throw Exception('Failed to update transaction: $e');
    }
  }

  /// 删除交易记录
  Future<bool> delete(String id) async {
    try {
      final db = await _databaseService.database;
      final key = await _store.record(id).delete(db);
      return key != null;
    } catch (e) {
      throw Exception('Failed to delete transaction: $e');
    }
  }

  /// 检查交易记录是否存在
  Future<bool> exists(String id) async {
    try {
      final transaction = await getById(id);
      return transaction != null;
    } catch (e) {
      throw Exception('Failed to check if transaction exists: $e');
    }
  }

  /// 获取交易记录总数
  Future<int> count() async {
    try {
      final db = await _databaseService.database;
      return await _store.count(db);
    } catch (e) {
      throw Exception('Failed to count transactions: $e');
    }
  }

  /// 计算总交易金额
  Future<double> getTotalAmount() async {
    try {
      final transactions = await getAll();
      return transactions.fold<double>(0.0, (sum, transaction) => sum + transaction.amount);
    } catch (e) {
      throw Exception('Failed to get total amount: $e');
    }
  }

  /// 根据交易方ID计算总交易金额
  Future<double> getTotalAmountByPartyId(String partyId) async {
    try {
      final transactions = await getByPartyId(partyId);
      return transactions.fold<double>(0.0, (sum, transaction) => sum + transaction.amount);
    } catch (e) {
      throw Exception('Failed to get total amount by party id: $e');
    }
  }
}
