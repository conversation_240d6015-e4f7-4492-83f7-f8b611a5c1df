# Invoice Automate

A Flutter application that uses AI to scan and extract information from receipts and invoices.

## Features

- 📸 **Camera Integration**: Take photos or select images from gallery
- 🤖 **AI-Powered Recognition**: Uses Google Gemini 2.5 Flash Lite Preview for fast and efficient extraction
- ✏️ **Editable Results**: Review and edit extracted information in a user-friendly interface
- 📊 **Structured Data**: Extracts merchant name, date, items, prices, and totals
- 🔒 **Secure**: API keys stored in environment variables
- 🚀 **Optimized**: Uses lightweight model with disabled thinking for faster responses
- 🛡️ **Robust**: Enhanced error handling and type safety

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd invoice_automate
flutter pub get
```

### 2. Configure Gemini API

1. Get your Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```
3. Edit `.env` and add your API key:
   ```
   GEMINI_API_KEY=your_actual_api_key_here
   ```

### 3. Run the Application

```bash
flutter run
```

## How to Use

1. **Launch the App**: Open the Invoice Automate application
2. **Check Configuration**: The app will show a status indicator for your API configuration
3. **Scan Receipt**: Tap the "Scan Receipt" button
4. **Take Photo**: Either take a new photo or select from gallery (supports JPG, PNG, WebP)
5. **Analyze**: Tap "Analyze Receipt" to process the image with AI
6. **Review & Edit**: Review the extracted data and make any necessary corrections
7. **Save**: Save the processed receipt data

## Troubleshooting

### Common Issues

1. **NotInitializedError**:
   - Make sure you have created a `.env` file in the project root
   - Ensure the `.env` file contains `GEMINI_API_KEY=your_api_key_here`
   - Restart the app after creating/modifying the `.env` file

2. **API Key Issues**:
   - Verify your Gemini API key is valid and active
   - Check that you have sufficient quota in your Google AI account
   - The app will show configuration status on the home screen

3. **Image Processing Issues**:
   - Supported formats: JPG, JPEG, PNG, WebP
   - Ensure the image is clear and well-lit
   - Try different angles or lighting conditions

4. **Type Conversion Errors**:
   - Fixed: `type 'double' is not a subtype of type 'int'` errors
   - The app now properly handles numeric type conversions from AI responses
   - Quantity values are automatically converted from double to int

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/
│   ├── receipt_data.dart     # Receipt data model
│   └── receipt_item.dart     # Individual item model
├── services/
│   └── gemini_service.dart   # Gemini API integration
└── pages/
    ├── camera_page.dart      # Camera and image selection
    └── receipt_result_page.dart # Results display and editing
```

## Dependencies

- `image_picker`: Camera and gallery access
- `http`: API requests to Gemini
- `flutter_dotenv`: Environment variable management
- `path_provider`: File system access
- `permission_handler`: Runtime permissions

## Permissions

The app requires the following permissions:
- **Camera**: To take photos of receipts
- **Storage**: To access gallery images
- **Internet**: To communicate with Gemini API

## Supported Platforms

- ✅ Android
- ✅ iOS (with additional setup)
- ⚠️ Web (limited camera functionality)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `flutter test`
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
