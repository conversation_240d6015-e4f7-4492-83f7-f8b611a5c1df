// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'package:invoice_automate/main.dart';

void main() {
  testWidgets('App starts correctly', (WidgetTester tester) async {
    // Initialize dotenv for testing
    dotenv.testLoad(fileInput: 'GEMINI_API_KEY=test_key');

    // Build our app and trigger a frame.
    await tester.pumpWidget(const InvoiceAutomateApp());

    // Verify that our app shows the correct title.
    expect(find.text('Invoice Automate'), findsOneWidget);
    expect(find.text('Invoice & Receipt Scanner'), findsOneWidget);

    // Verify that the scan button is present.
    expect(find.text('Scan Receipt'), findsOneWidget);
  });
}
