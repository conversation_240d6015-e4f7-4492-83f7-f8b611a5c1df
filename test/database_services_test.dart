import 'package:flutter_test/flutter_test.dart';
import 'package:invoice_automate/models/party.dart';
import 'package:invoice_automate/models/transaction.dart' as models;
import 'package:invoice_automate/models/invoice.dart';
import 'package:invoice_automate/services/database_service.dart';
import 'package:invoice_automate/services/party_service.dart';
import 'package:invoice_automate/services/transaction_service.dart';
import 'package:invoice_automate/services/invoice_service.dart';

void main() {
  group('Database Services Tests', () {
    late DatabaseService databaseService;
    late PartyService partyService;
    late TransactionService transactionService;
    late InvoiceService invoiceService;

    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
      databaseService = DatabaseService();
      databaseService.setTestMode(true); // 启用测试模式
      partyService = PartyService();
      transactionService = TransactionService();
      invoiceService = InvoiceService();
    });

    tearDownAll(() async {
      // 清理测试数据
      await databaseService.clearAllData();
      await databaseService.close();
    });

    group('Party Service Tests', () {
      test('should create and retrieve a party', () async {
        final party = Party(
          name: 'Test Company',
          abn: '*********01',
          tfn: '*********',
          phoneNumber: '+61 2 1234 5678',
          address: '123 Test St, Sydney NSW 2000',
          type: PartyType.supplier,
        );

        // 创建交易方
        final createdParty = await partyService.create(party);
        expect(createdParty.id, isNotEmpty);
        expect(createdParty.name, equals('Test Company'));

        // 根据ID获取交易方
        final retrievedParty = await partyService.getById(createdParty.id);
        expect(retrievedParty, isNotNull);
        expect(retrievedParty!.name, equals('Test Company'));
        expect(retrievedParty.type, equals(PartyType.supplier));
      });

      test('should get parties by type', () async {
        final supplier = Party(
          name: 'Supplier Company',
          type: PartyType.supplier,
        );
        
        final customer = Party(
          name: 'Customer Company',
          type: PartyType.customer,
        );

        await partyService.create(supplier);
        await partyService.create(customer);

        final suppliers = await partyService.getByType(PartyType.supplier);
        final customers = await partyService.getByType(PartyType.customer);

        expect(suppliers.length, greaterThanOrEqualTo(1));
        expect(customers.length, greaterThanOrEqualTo(1));
      });

      test('should update party information', () async {
        final party = Party(
          name: 'Original Name',
          type: PartyType.supplier,
        );

        final createdParty = await partyService.create(party);
        final updatedParty = createdParty.copyWith(
          name: 'Updated Name',
          phoneNumber: '+61 2 9999 8888',
        );

        await partyService.update(updatedParty);
        final retrievedParty = await partyService.getById(createdParty.id);

        expect(retrievedParty!.name, equals('Updated Name'));
        expect(retrievedParty.phoneNumber, equals('+61 2 9999 8888'));
      });
    });

    group('Transaction Service Tests', () {
      late Party testParty;

      setUp(() async {
        testParty = Party(
          name: 'Test Party for Transactions',
          type: PartyType.customer,
        );
        testParty = await partyService.create(testParty);
      });

      test('should create and retrieve a transaction', () async {
        final transaction = models.Transaction(
          partyId: testParty.id,
          amount: 1500.00,
          transactionTime: DateTime.now(),
          description: 'Test transaction',
        );

        final createdTransaction = await transactionService.create(transaction);
        expect(createdTransaction.id, isNotEmpty);
        expect(createdTransaction.amount, equals(1500.00));

        final retrievedTransaction = await transactionService.getById(createdTransaction.id);
        expect(retrievedTransaction, isNotNull);
        expect(retrievedTransaction!.partyId, equals(testParty.id));
      });

      test('should get transactions by party id', () async {
        final transaction1 = models.Transaction(
          partyId: testParty.id,
          amount: 1000.00,
          transactionTime: DateTime.now(),
        );

        final transaction2 = models.Transaction(
          partyId: testParty.id,
          amount: 2000.00,
          transactionTime: DateTime.now(),
        );

        await transactionService.create(transaction1);
        await transactionService.create(transaction2);

        final transactions = await transactionService.getByPartyId(testParty.id);
        expect(transactions.length, greaterThanOrEqualTo(2));
      });

      test('should calculate total amount', () async {
        final initialTotal = await transactionService.getTotalAmount();

        final transaction = models.Transaction(
          partyId: testParty.id,
          amount: 500.00,
          transactionTime: DateTime.now(),
        );

        await transactionService.create(transaction);
        final newTotal = await transactionService.getTotalAmount();
        
        expect(newTotal, equals(initialTotal + 500.00));
      });
    });

    group('Invoice Service Tests', () {
      late Party testParty;
      late models.Transaction testTransaction;

      setUp(() async {
        testParty = Party(
          name: 'Test Party for Invoices',
          type: PartyType.customer,
        );
        testParty = await partyService.create(testParty);

        testTransaction = models.Transaction(
          partyId: testParty.id,
          amount: 3000.00,
          transactionTime: DateTime.now(),
        );
        testTransaction = await transactionService.create(testTransaction);
      });

      test('should create and retrieve an invoice', () async {
        final invoice = Invoice(
          transactionId: testTransaction.id,
          issuedAt: DateTime.now(),
          invoiceNumber: 'TEST-001',
          status: InvoiceStatus.draft,
        );

        final createdInvoice = await invoiceService.create(invoice);
        expect(createdInvoice.id, isNotEmpty);
        expect(createdInvoice.invoiceNumber, equals('TEST-001'));

        final retrievedInvoice = await invoiceService.getById(createdInvoice.id);
        expect(retrievedInvoice, isNotNull);
        expect(retrievedInvoice!.transactionId, equals(testTransaction.id));
      });

      test('should get invoices by status', () async {
        final draftInvoice = Invoice(
          transactionId: testTransaction.id,
          issuedAt: DateTime.now(),
          status: InvoiceStatus.draft,
        );

        await invoiceService.create(draftInvoice);
        final draftInvoices = await invoiceService.getByStatus(InvoiceStatus.draft);
        
        expect(draftInvoices.length, greaterThanOrEqualTo(1));
      });

      test('should update invoice status', () async {
        final invoice = Invoice(
          transactionId: testTransaction.id,
          issuedAt: DateTime.now(),
          status: InvoiceStatus.draft,
        );

        final createdInvoice = await invoiceService.create(invoice);
        await invoiceService.updateStatus(createdInvoice.id, InvoiceStatus.sent);

        final updatedInvoice = await invoiceService.getById(createdInvoice.id);
        expect(updatedInvoice!.status, equals(InvoiceStatus.sent));
      });
    });
  });
}
