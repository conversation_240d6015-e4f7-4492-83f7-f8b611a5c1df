import 'package:flutter_test/flutter_test.dart';
import 'package:invoice_automate/models/receipt_item.dart';
import 'package:invoice_automate/models/receipt_data.dart';

void main() {
  group('ReceiptItem', () {
    test('should handle double quantity conversion', () {
      final json = {
        'name': 'Test Item',
        'price': 10.99,
        'quantity': 2.0, // Double value that should be converted to int
        'total': 21.98,
      };

      final item = ReceiptItem.fromJson(json);

      expect(item.name, 'Test Item');
      expect(item.price, 10.99);
      expect(item.quantity, 2); // Should be int
      expect(item.total, 21.98);
    });

    test('should handle int quantity', () {
      final json = {
        'name': 'Test Item',
        'price': 5.50,
        'quantity': 3, // Int value
        'total': 16.50,
      };

      final item = ReceiptItem.fromJson(json);

      expect(item.quantity, 3);
    });

    test('should handle missing values with defaults', () {
      final json = <String, dynamic>{
        'name': 'Test Item',
      };

      final item = ReceiptItem.fromJson(json);

      expect(item.name, 'Test Item');
      expect(item.price, 0.0);
      expect(item.quantity, 1);
      expect(item.total, 0.0);
    });
  });

  group('ReceiptData', () {
    test('should parse complete receipt data', () {
      final json = {
        'merchant_name': 'Test Store',
        'date': '2024-01-01',
        'address': '123 Test St',
        'items': [
          {
            'name': 'Item 1',
            'price': 10.0,
            'quantity': 2.0,
            'total': 20.0,
          }
        ],
        'subtotal': 20.0,
        'tax': 2.0,
        'total': 22.0,
        'currency': 'USD',
      };

      final receipt = ReceiptData.fromJson(json);

      expect(receipt.merchantName, 'Test Store');
      expect(receipt.date, '2024-01-01');
      expect(receipt.address, '123 Test St');
      expect(receipt.items.length, 1);
      expect(receipt.items[0].quantity, 2); // Should be int
      expect(receipt.subtotal, 20.0);
      expect(receipt.tax, 2.0);
      expect(receipt.total, 22.0);
      expect(receipt.currency, 'USD');
    });
  });
}
